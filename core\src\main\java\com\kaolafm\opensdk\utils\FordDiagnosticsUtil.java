package com.kaolafm.opensdk.utils;

import android.app.Application;
import android.content.Context;
import android.os.Looper;
import android.util.Log;

import com.ford.fnv.diagnostics.FnvAnalyticsEventType;
import com.ford.fnv.diagnostics.FnvAnalyticsLog;
import com.ford.fnv.diagnostics.IDiagnosticsEventBuilder;
import com.ford.sync.fnvservice.FnvConstants;
import com.ford.sync.fnvservice.FordFnv;
import com.ford.sync.fnvservice.FordFnvServiceListener;
import com.ford.sync.fnvservice.diagnostics.DiagnosticsManager;
import com.kaolafm.opensdk.di.component.ComponentKit;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class FordDiagnosticsUtil {
    private static final String TAG = "EventLog";

    // 单例实例
    private static volatile FordDiagnosticsUtil instance;

    // 线程池
    private final ExecutorService diagnosticsExecutor;

    FordFnv mFordFnv;
    // 私有构造函数
    private FordDiagnosticsUtil() {
        // 创建单线程线程池，确保诊断事件按顺序发送
        diagnosticsExecutor = Executors.newCachedThreadPool();

        Application app = ComponentKit.getInstance().getApplication();
        Context context = app != null ? app.getApplicationContext() : null;
        if(context != null){
            FordFnv.createFordFnv(context, new FordFnvServiceListener() {
                @Override
                public void onServiceConnect(FordFnv fordFnv) {
                    mFordFnv=fordFnv;

                }

                @Override
                public void onServiceDisconnect() {
                    mFordFnv=null;
                    Log.d(TAG, "FordFnv service disconnected");
                }
            });
        }else{
            Log.d(TAG, "context is Null");
        }

    }

    // 获取单例实例
    public static FordDiagnosticsUtil getInstance() {
        if (instance == null) {
            synchronized (FordDiagnosticsUtil.class) {
                if (instance == null) {
                    instance = new FordDiagnosticsUtil();
                }
            }
        }
        return instance;
    }

    public void reportFordDisconnect(Context context) {
        if (mFordFnv==null){
            Log.i(TAG,"mFordFnv is first");
            FordFnv.createFordFnv(context, new FordFnvServiceListener() {
                @Override
                public void onServiceConnect(FordFnv fordFnv) {
                    mFordFnv=fordFnv;
                    diagnosticsExecutor.execute(new DiagnosticsTask(mFordFnv));
                }

                @Override
                public void onServiceDisconnect() {
                    mFordFnv=null;
                    Log.d(TAG, "FordFnv service disconnected");
                }
            });
        }else{
            Log.i(TAG,"mFordFnv Not Null");
            diagnosticsExecutor.execute(new DiagnosticsTask(mFordFnv));
        }

    }

    // 将诊断操作封装为独立的任务类
    private static class DiagnosticsTask implements Runnable {
        private final FordFnv fordFnv;

        DiagnosticsTask(FordFnv fordFnv) {
            this.fordFnv = fordFnv;
        }

        @Override
        public void run() {
            try {
                DiagnosticsManager diagnosticsManager = (DiagnosticsManager)
                        fordFnv.getFnvManager(FnvConstants.DIAGNOSTICS_SERVICE);

                IDiagnosticsEventBuilder builder = diagnosticsManager.getDiagnosticsEventBuilder(
                        FnvAnalyticsEventType.LW_EVENT_SYNC_APPLINK_APP_UNEXPECTED_DISCONNECT);

                int[] logArray = new int[]{
                        FnvAnalyticsLog.DuerOSLogLogcat,
                        FnvAnalyticsLog.DuerOSLogANR,
                };
                builder.fnvAddLogs(logArray);
                byte result = builder.fnvBuild().fnvSendAndWait();
                Log.i(TAG, "Diagnostics report sent, result: " + result);
            } catch (Exception e) {
                Log.e(TAG, "Failed to send diagnostics report", e);
            }

        }
    }
}