package com.kaolafm.ad;

import android.app.Application;

import com.kaolafm.ad.api.internal.AdReportRequest;
import com.kaolafm.ad.profile.AdProfileManager;
import com.kaolafm.ad.report.AdReportManager;
import com.kaolafm.ad.report.MonitorParameterManager;
import com.kaolafm.ad.report.net.AdReportNetHelper;
import com.kaolafm.ad.timer.TimedAdvertManager;
import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.BaseEngine;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import javax.inject.Inject;

import dagger.Lazy;

/**
 * 广告SDK的内部入口，这里是广告SDK初始化/激活逻辑部分。
 * <AUTHOR>
 * @date 2020-03-05
 */
@AppScope
public class AdvertisingInternalEngine extends BaseEngine<AdvertOptions, AdProfileManager> {

    private static final String ADVERT_ACTIVATE = "advert_activate";

    @Inject
    @AppScope
    Lazy<AdReportRequest> reportRequestLazy;

    @Inject
    public AdvertisingInternalEngine() {
    }

    @Override
    public void internalInit(Application application, AdvertOptions options, HttpCallback<Boolean> callback) {
        SpUtil.init(application, AdConstant.SP_NAME);
        mProfileManager.loadProfile();
        AdReportNetHelper.getInstance().setReportRequest(new AdReportRequest());
        AdReportManager.getInstance().init(application);
    }

    @Override
    public void internalActivate(HttpCallback<Boolean> callback) {
        reportRequestLazy.get().active(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                saveActivation();
                if (callback != null) {
                    callback.onSuccess(true);
                }
            }

            @Override
            public void onError(ApiException exception) {
                if (callback != null) {
                    callback.onError(exception);
                }
            }
        });
    }

    @Override
    public boolean isActivated() {
        return SpUtil.getBoolean(AdConstant.SP_NAME, ADVERT_ACTIVATE, false);
    }

    @Override
    public void config(Application application, AdvertOptions options, HttpCallback<Boolean> callback) {
        init(application, options, callback);
        //不需要关心是否激活上报成功。
        activate(null);
    }

    private void saveActivation() {
        SpUtil.putBoolean(AdConstant.SP_NAME, ADVERT_ACTIVATE, true);
    }

    @Override
    public void release() {
        TimedAdvertManager.getInstance().stop();
    }

    @Override
    public void setLocation(String lng, String lat) {
        super.setLocation(lng, lat);
        MonitorParameterManager.getInstance().setLocation(lng, lat);
    }

    @Override
    public void setLocation(String lng, String lat, String coordType) {
        if (StringUtil.isEmpty(coordType)) {
            setLocation(lng, lat);
        } else {
            super.setLocation(lng, lat, coordType);
            MonitorParameterManager.getInstance().setLocation(lng, lat, coordType);
        }
    }

    @Override
    public void clearAll() {
    }

    public void setBrand(String brand) {
        mProfileManager.setBrand(brand);
    }
}
