-include ../core/proguard-core-rules.pro
-include ../ad/proguard-advert-rules.pro

# 这句话能够使我们的项目混淆后产生映射文件
# 包含有类名->混淆后类名的映射关系
-verbose
-printmapping proguardMapping-SDK.txt

#项目
-dontwarn com.kaolafm.opensdk.**
#-keepclasseswithmembernames class com.kaolafm.opensdk.api.** { *;}


-keep class com.kaolafm.opensdk.player.** { *; }
-keep class com.kaolafm.opensdk.OpenSDK {
    public *;
}
-keep class com.kaolafm.opensdk.utils.* { *; }
-keep class com.kaolafm.opensdk.utils.operation.OperationAssister{*;}
-keepclasseswithmembernames public class com.kaolafm.opensdk.ResType {<fields>;}
-keep class * implements com.kaolafm.opensdk.di.component.AppComponent {*;}

-keepclassmembers class com.kaolafm.opensdk.api.music.qq.QQMusicTokenInterceptor {
    okhttp3.Response intercept(okhttp3.Interceptor$Chain);
}

-keepclasseswithmembernames public class com.kaolafm.opensdk.account.token.AccessTokenManager {
   public *;
}

#-keepclasseswithmembernames class com.kaolafm.opensdk.di.component.** {*;}
#-dontwarn com.kaolafm.opensdk.**
#-keep class com.kaolafm.opensdk.** { *; }

-dontwarn com.kaolafm.sdk.**
-keep class com.kaolafm.sdk.** { *; }


-dontwarn tv.danmaku.ijk.**
-keep class tv.danmaku.ijk.** { *; }
-keepclasseswithmembers class tv.danmaku.ijk.media.player.IjkMediaPlayer {
    <fields>;
    <methods>;
}

-keepclasseswithmembernames class com.kaolafm.opensdk.api.**.model.** { *;}
-keepclasseswithmembernames class com.kaolafm.opensdk.api.**.*Request { *;}
-keepclasseswithmembernames class com.kaolafm.opensdk.api.broadcast.BroadcastDetails { *;}
-keepclasseswithmembernames class com.kaolafm.opensdk.api.broadcast.ProgramDetails { *;}
#保持场景推送子类
-keepclasseswithmembernames class * extends com.kaolafm.opensdk.api.scene.Scene { *;}
-keepclasseswithmembernames class com.kaolafm.opensdk.api.scene.SceneInfo { *;}
-keepclasseswithmembernames class com.kaolafm.opensdk.api.search.model.SearchProgramBean { *;}
-keepclasseswithmembernames class com.kaolafm.opensdk.api.search.model.VoiceSearchResult { *;}
-keepclasseswithmembernames class com.kaolafm.opensdk.api.subscribe.SubscribeInfo { *;}
-keepclasseswithmembernames class com.kaolafm.opensdk.api.subscribe.SubscribeStatus { *;}
-keepclasseswithmembernames class com.kaolafm.opensdk.api.BasePageResult { *;}


#-keepclassmembers class com.kaolafm.opensdk.api.** { *;}
-keepclasseswithmembernames class com.kaolafm.opensdk.log.** { *;}
-keep class com.kaolafm.opensdk.log.** { *;}

-dontoptimize