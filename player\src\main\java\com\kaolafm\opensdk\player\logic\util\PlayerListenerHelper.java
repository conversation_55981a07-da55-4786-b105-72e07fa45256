package com.kaolafm.opensdk.player.logic.util;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.NetworkUtil;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.KLAudioStateChangedByAudioFocusListener;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.IGeneralListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateVideoListener;
import com.kaolafm.opensdk.player.logic.model.item.TempTaskPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class PlayerListenerHelper {
    private static final String TAG = "PlayerListenerHelper";
    /**
     * 播放器状态回调
     * do AlbumPlayControl#requestPlayUrl#new AudioRequest().getAudioPlayInfo#onError#mBasePlayControlListener.onPlayerFailed
     * do PlayerManager.getInstance().addPlayControlStateCallback()
     */
    private final CopyOnWriteArrayList<IPlayerStateListener> mPlayControlListenerArrayList;
    /**
     * 播放器播单状态回调
     */
    private final CopyOnWriteArrayList<IPlayListStateListener> mPlayListControlListenerArrayList;

    /**
     * 播放器初始化
     */
    private final CopyOnWriteArrayList<IPlayerInitCompleteListener> mPlayerInitCompleteListenerArrayList;
    private final CopyOnWriteArrayList<IPlayerStateVideoListener> mPlayerStateVideoListenerArrayList;

    /**
     * 音频焦点队列
     */
    private final CopyOnWriteArrayList<OnAudioFocusChangeInter> mAudioFocusListenerArrayList;

    /**
     * 通用状态回调
     * do LauncherActivity#PlayerManager.getInstance().addGeneralListener(generalListener);
     */
    private final CopyOnWriteArrayList<IGeneralListener> mGeneralListenerArrayList;

    /**
     * 播单变化 内部回调
     */
    private final MIPlayListStateListener miPlayListStateListener;
    /**
     * 播放状态 内部回调
     */
    private final MBasePlayStateListener mBasePlayStateListener;

    /**
     * 播放器初始化完成
     */
    private final MPlayerInitCompleteListener mPlayerInitCompleteListener;
    private final MPlayerlStateVideoListener mPlayerStateVideoListener;

    /**
     * 音频焦点
     */
    private final MOnAudioFocusChangeInter mOnAudioFocusChangeInter;

    /**
     * 是否因为音频焦点导致的暂停
     */
    private final MAudioStateChangedByAudioFocusListener mAudioStateChangedByAudioFocusListener;

    /**
     * 播放器是否初始化成功
     */
    private boolean isPlayerInitSuccess = false;

    /**
     * 当前音频流类型
     */
    private int mAudioFocusStatus;

    private int mType;

    private PlayItem mPlayItem;

    /**
     * 记录播放结束的播放项，用于在下一首开始准备时重置进度
     */
    private PlayItem mEndedPlayItem;

    /**
     * 是否是由用户出发操作
     */
    private volatile boolean isPauseFromUser = false;

    public PlayerListenerHelper() {
        mPlayControlListenerArrayList = new CopyOnWriteArrayList<>();
        mPlayerStateVideoListenerArrayList = new CopyOnWriteArrayList<>();
        mPlayListControlListenerArrayList = new CopyOnWriteArrayList<>();
        mPlayerInitCompleteListenerArrayList = new CopyOnWriteArrayList<>();
        mAudioFocusListenerArrayList = new CopyOnWriteArrayList<>();
        mGeneralListenerArrayList = new CopyOnWriteArrayList<>();

        miPlayListStateListener = new MIPlayListStateListener(this);
        mBasePlayStateListener = new MBasePlayStateListener(this);
        mPlayerInitCompleteListener = new MPlayerInitCompleteListener(this);
        mPlayerStateVideoListener = new MPlayerlStateVideoListener(this);
        mOnAudioFocusChangeInter = new MOnAudioFocusChangeInter(this);
        mAudioStateChangedByAudioFocusListener = new MAudioStateChangedByAudioFocusListener(this);
    }


    private void notifyListChange(List<PlayItem> playItemArrayList) {
        if (ListUtil.isEmpty(mPlayListControlListenerArrayList)) {
            return;
        }
        for (IPlayListStateListener listener : mPlayListControlListenerArrayList) {
            if (listener != null) {
                listener.onPlayListChange(playItemArrayList);
            }
        }
    }

    private void notifyPlayListError(PlayItem playItem, int errorCode, int errorExtra) {
        if (ListUtil.isEmpty(mPlayListControlListenerArrayList)) {
            return;
        }
        for (IPlayListStateListener listener : mPlayListControlListenerArrayList) {
            if (listener != null) {
                listener.onPlayListChangeError(playItem, errorCode, errorExtra);
            }
        }

    }

    public void notifyPlayControl(int type, PlayItem playItem) {
        notifyPlayControl(type, playItem, 0, 0);
    }

    private void notifyPlayControl(int type, PlayItem playItem, long l, long l2) {
        if (type != PlayerConstants.TYPE_PLAYER_DOWNLOAD_PROGRESS && type != PlayerConstants.TYPE_PLAYER_PROGRESS) {
            mType = type;
            mPlayItem = playItem;
        }
        if (ListUtil.isEmpty(mPlayControlListenerArrayList)) {
            return;
        }

        for (IPlayerStateListener listener : mPlayControlListenerArrayList) {
            if (listener != null) {
                notifyPlayState(listener, playItem, type, l, l2); //l(what,即错误码),l2(extra)
            }
        }
    }

    private void notifyPlayState(IPlayerStateListener listener, PlayItem playItem, int type, long l1, long l2) {
        switch (type) {
            case PlayerConstants.TYPE_PLAYER_IDLE:
                listener.onIdle(playItem);
                break;
            case PlayerConstants.TYPE_PLAYER_PREPARING:
                listener.onPlayerPreparing(playItem);
                break;
            case PlayerConstants.TYPE_PLAYER_PREPARING_COMPLETE:
                listener.onPlayerPreparingComplete(playItem);
                break;
            case PlayerConstants.TYPE_PLAYER_PLAYING:
                listener.onPlayerPlaying(playItem);
                break;
            case PlayerConstants.TYPE_PLAYER_PROGRESS:
                listener.onProgress(playItem, l1, l2);
                break;
            case PlayerConstants.TYPE_PLAYER_PAUSED:
                listener.onPlayerPaused(playItem);
                break;
            case PlayerConstants.TYPE_SEEK_START:
                listener.onSeekStart(playItem);
                break;
            case PlayerConstants.TYPE_SEEK_COMPLETE:
                listener.onSeekComplete(playItem);
                break;
            case PlayerConstants.TYPE_BUFFERING_START:
                listener.onBufferingStart(playItem);
                break;
            case PlayerConstants.TYPE_BUFFERING_END:
                listener.onBufferingEnd(playItem);
                break;
            case PlayerConstants.TYPE_PLAYER_END:
                listener.onPlayerEnd(playItem);
                break;
            case PlayerConstants.TYPE_PLAYER_FAILED: //error type
                listener.onPlayerFailed(playItem, (int) l1, (int) l2); //l1(error code),l2(type msg)
                break;
            case PlayerConstants.TYPE_PLAYER_DOWNLOAD_PROGRESS:
                listener.onDownloadProgress(playItem, l1, l2);
                break;
            case PlayerConstants.TYPE_PLAYER_VIDEO_RENDER_START:
                if(listener instanceof IPlayerStateVideoListener){
                    ((IPlayerStateVideoListener)listener).onPlayerVideoRenderingStart(playItem);
                }
                break;
            case PlayerConstants.TYPE_PLAYER_VIDEO_SIZE_CHANGED:
                if(listener instanceof IPlayerStateVideoListener){
                    ((IPlayerStateVideoListener)listener).onPlayerVideoSizeChanged(playItem, (int)l1, (int)l2);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 播放状态监听
     */
    public static class MBasePlayStateListener extends BasePlayStateListener {
        PlayerListenerHelper playerListenerHelper;

        public MBasePlayStateListener(PlayerListenerHelper playerListenerHelper) {
            this.playerListenerHelper = playerListenerHelper;
        }

        @Override
        public void onIdle(PlayItem playItem) {
            PlayerLogUtil.log(TAG, "MBasePlayStateListener->onIdle");
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_IDLE, playItem));
        }

        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            PlayerLogUtil.log(TAG, "MBasePlayStateListener->onPlayerPreparing");
            //重置上一个播放结束项的进度
            if (playerListenerHelper.mEndedPlayItem != null && playerListenerHelper.mEndedPlayItem != playItem) {
                long position = playerListenerHelper.mEndedPlayItem.getPosition();
                long duration = playerListenerHelper.mEndedPlayItem.getDuration();
                if (duration > 0 && position >= duration - 1000) {
                    playerListenerHelper.mEndedPlayItem.setPosition(0);
                }
                playerListenerHelper.mEndedPlayItem = null; // 重置后清空记录
            }
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_PREPARING, playItem));
        }

        @Override
        public void onPlayerPreparingComplete(PlayItem playItem) {
            PlayerLogUtil.log(TAG, "MBasePlayStateListener->onPlayerPreparingComplete");
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_PREPARING_COMPLETE, playItem));
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            PlayerLogUtil.log(TAG, "MBasePlayStateListener->onPlayerPlaying");
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_PLAYING, playItem));
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            PlayerLogUtil.log(TAG, "MBasePlayStateListener->onPlayerPaused");
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_PAUSED, playItem));
        }

        @Override
        public void onProgress(PlayItem playItem, long progress, long total) {
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_PROGRESS, playItem, progress, total));
        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int what, int extra) { //what（error code）,extra(msg)
            PlayerLogUtil.log(TAG, "MBasePlayStateListener->onPlayerFailed: " + what + ", " + extra);
            playerListenerHelper.isPauseFromUser = false;
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_FAILED, playItem, what, extra));
        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {
            PlayerLogUtil.log(TAG, "MBasePlayStateListener->onPlayerEnd");
            playEnd(playItem);
        }

        @Override
        public void onSeekStart(PlayItem playItem) {
            PlayerLogUtil.log(TAG, "MBasePlayStateListener->onSeekStart");
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_SEEK_START, playItem));
        }

        @Override
        public void onSeekComplete(PlayItem playItem) {
            PlayerLogUtil.log(TAG, "MBasePlayStateListener->onSeekComplete");
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_SEEK_COMPLETE, playItem));
        }

        @Override
        public void onBufferingStart(PlayItem playItem) {
            PlayerLogUtil.log(TAG, "MBasePlayStateListener->onBufferingStart");
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_BUFFERING_START, playItem));
        }

        @Override
        public void onBufferingEnd(PlayItem playItem) {
            PlayerLogUtil.log(TAG, "MBasePlayStateListener->onBufferingEnd");
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_BUFFERING_END, playItem));
        }

        @Override
        public void onDownloadProgress(PlayItem playItem, long progress, long total) {
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_DOWNLOAD_PROGRESS, playItem, progress, total));
        }

        private void playEnd(PlayItem playItem) {
            PlayerLogUtil.log(TAG, "MBasePlayStateListener->playEnd");
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_END, playItem));
            //记录播放结束的播放项，用于在下一首开始准备时重置进度
            if (playItem != null && PlayerManager.getInstance().hasNext()) {
                playerListenerHelper.mEndedPlayItem = playItem;
            }
            if (!NetworkUtil.isNetworkAvailable(PlayerManager.getInstance().getContext())) {
                playerListenerHelper.notifyPlayError(-1);
                playerListenerHelper.isPauseFromUser = false;
                return;
            }
            if(PlayerManager.getInstance().isAutoPlay()){
                if(playItem instanceof TempTaskPlayItem){
                    return;
                }
                if(PlayItemUtil.isVideo(playItem)){
                    if(PlayerManager.getInstance().hasNext()){
                        PlayerManager.getInstance().playNext(false);
                    }else {
                        PlayerManager.getInstance().rePlay();
                    }
                    return;
                }
                PlayerManager.getInstance().playNext(false);
            }
        }
    }
    /**
     * 播单变化监听
     */
    public static class MIPlayListStateListener implements IPlayListStateListener {
        PlayerListenerHelper playerListenerHelper;

        public MIPlayListStateListener(PlayerListenerHelper playerListenerHelper) {
            this.playerListenerHelper = playerListenerHelper;
        }

        @Override
        public void onPlayListChange(List<PlayItem> playItemArrayList) {
            UIThreadUtil.runUIThread(() ->   playerListenerHelper.notifyListChange(playItemArrayList));

        }

        @Override
        public void onPlayListChangeError(PlayItem playItem, int errorCode, int errorExtra) {
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayListError(playItem, errorCode, errorExtra));
        }
    }

    /**
     * 添加播单改变监听
     */
    public void addPlayListControlStateCallback(IPlayListStateListener iPlayListControlListener) {
        if (mPlayListControlListenerArrayList.contains(iPlayListControlListener)) {
            return;
        }
        mPlayListControlListenerArrayList.add(iPlayListControlListener);
    }

    /**
     * 删除播单改变监听
     */
    public void removePlayListControlStateCallback(IPlayListStateListener iPlayListControlListener) {
        if (ListUtil.isEmpty(mPlayListControlListenerArrayList)) {
            return;
        }
        mPlayListControlListenerArrayList.remove(iPlayListControlListener);
    }

    /**
     * 添加播放状态监听
     */
    public void addPlayControlStateCallback(IPlayerStateListener iPlayControlListener) {
        if (mPlayControlListenerArrayList.contains(iPlayControlListener)) {
            return;
        }
        if (mType > 0 && mPlayItem != null) {
            notifyPlayState(iPlayControlListener, mPlayItem, mType, 0, 0);
        }
        mPlayControlListenerArrayList.add(iPlayControlListener);
    }

    /**
     * 删除播放状态监听
     */
    public void removePlayControlStateCallback(IPlayerStateListener iPlayControlListener) {
        if (ListUtil.isEmpty(mPlayControlListenerArrayList)) {
            return;
        }
        mPlayControlListenerArrayList.remove(iPlayControlListener);
    }

    public static class MPlayerInitCompleteListener implements IPlayerInitCompleteListener {
        PlayerListenerHelper playerListenerHelper;

        public MPlayerInitCompleteListener(PlayerListenerHelper playerListenerHelper) {
            this.playerListenerHelper = playerListenerHelper;
        }

        @Override
        public void onPlayerInitComplete(boolean flag) {
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayerInitComplete());
        }
    }

    public void addPlayerInitComplete(IPlayerInitCompleteListener iPlayerInitCompleteListener) {
        if (mPlayerInitCompleteListenerArrayList.contains(iPlayerInitCompleteListener)) {
            return;
        }
        mPlayerInitCompleteListenerArrayList.add(iPlayerInitCompleteListener);
    }


    public void removePlayerInitComplete(IPlayerInitCompleteListener iPlayerInitCompleteListener) {
        if (ListUtil.isEmpty(mPlayerInitCompleteListenerArrayList)) {
            return;
        }
        mPlayerInitCompleteListenerArrayList.remove(iPlayerInitCompleteListener);
    }

    private void notifyPlayerInitComplete() {
        isPlayerInitSuccess = true;
        if (ListUtil.isEmpty(mPlayerInitCompleteListenerArrayList)) {
            return;
        }
        for (IPlayerInitCompleteListener listener : mPlayerInitCompleteListenerArrayList) {
            if (listener != null) {
                listener.onPlayerInitComplete(true);
            }
        }

    }

    public static class MPlayerlStateVideoListener implements IPlayerStateVideoListener {
        PlayerListenerHelper playerListenerHelper;

        public MPlayerlStateVideoListener(PlayerListenerHelper playerListenerHelper) {
            this.playerListenerHelper = playerListenerHelper;
        }

        @Override
        public void onPlayerVideoRenderingStart(PlayItem playItem) {
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyVideoRenderingStart(playItem));
        }

        @Override
        public void onPlayerVideoSizeChanged(PlayItem playItem, int width, int height) {
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayerVideoSizeChanged(playItem, width, height));
        }
    }

    public void addPlayerControlStateVideoCallBack(IPlayerStateVideoListener iPlayerStateVideoListener) {
        if (mPlayerStateVideoListenerArrayList.contains(iPlayerStateVideoListener)) {
            return;
        }
        mPlayerStateVideoListenerArrayList.add(iPlayerStateVideoListener);
    }


    public void removePlayerControlStateVideoCallBack(IPlayerStateVideoListener iPlayerStateVideoListener) {
        if (ListUtil.isEmpty(mPlayerStateVideoListenerArrayList)) {
            return;
        }
        mPlayerStateVideoListenerArrayList.remove(iPlayerStateVideoListener);
    }

    private void notifyVideoRenderingStart(PlayItem playItem){
        if (ListUtil.isEmpty(mPlayerStateVideoListenerArrayList)) {
            return;
        }
        for (IPlayerStateVideoListener listener : mPlayerStateVideoListenerArrayList) {
            if (listener != null) {
                listener.onPlayerVideoRenderingStart(playItem);
            }
        }
    }

    private void notifyPlayerVideoSizeChanged(PlayItem playItem, int width, int height) {
        if (ListUtil.isEmpty(mPlayerStateVideoListenerArrayList)) {
            return;
        }
        for (IPlayerStateVideoListener listener : mPlayerStateVideoListenerArrayList) {
            if (listener != null) {
                listener.onPlayerVideoSizeChanged(playItem, width, height);
            }
        }
    }

    public static class MOnAudioFocusChangeInter implements OnAudioFocusChangeInter {
        PlayerListenerHelper playerListenerHelper;

        public MOnAudioFocusChangeInter(PlayerListenerHelper playerListenerHelper) {
            this.playerListenerHelper = playerListenerHelper;
        }

        @Override
        public void onAudioFocusChange(int focusChange) {
            PlayerLogUtil.log(getClass().getSimpleName(), "MOnAudioFocusChangeInter->onAudioFocusChange: " + focusChange);
            playerListenerHelper.mAudioFocusStatus = focusChange;
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyAudioFocus(focusChange));
        }
    }

    public void notifyAudioFocus(int state) {
        if (ListUtil.isEmpty(mAudioFocusListenerArrayList)) {
            return;
        }
        for (OnAudioFocusChangeInter listener : mAudioFocusListenerArrayList) {
            if (listener != null) {
                listener.onAudioFocusChange(state);
            }
        }
    }

    public void addAudioFocusListener(OnAudioFocusChangeInter iAudioFocusListener) {
        if (PlayerPreconditions.checkNull(iAudioFocusListener)) {
            return;
        }
        if (mAudioFocusListenerArrayList.contains(iAudioFocusListener)) {
            return;
        }
        mAudioFocusListenerArrayList.add(iAudioFocusListener);
    }

    public void removeAudioFocusListener(OnAudioFocusChangeInter iAudioFocusListener) {
        if (PlayerPreconditions.checkNull(iAudioFocusListener)) {
            return;
        }
        mAudioFocusListenerArrayList.remove(iAudioFocusListener);
    }

    public static class MAudioStateChangedByAudioFocusListener implements KLAudioStateChangedByAudioFocusListener {
        PlayerListenerHelper playerListenerHelper;

        public MAudioStateChangedByAudioFocusListener(PlayerListenerHelper playerListenerHelper) {
            this.playerListenerHelper = playerListenerHelper;
        }

        @Override
        public void onAudioStatePausedByLossAudioFocus() {
            playerListenerHelper.isPauseFromUser = false;
        }

        @Override
        public void onAudioStatePlayingByGainAudioFocus() {
        }
    }


    public MIPlayListStateListener getPlayListStateListener() {
        return miPlayListStateListener;
    }

    public MBasePlayStateListener getBasePlayStateListener() {
        return mBasePlayStateListener;
    }

    public MPlayerInitCompleteListener getPlayerInitCompleteListener() {
        return mPlayerInitCompleteListener;
    }
    public MPlayerlStateVideoListener getPlayerStateVideoListener() {
        return mPlayerStateVideoListener;
    }

    public MOnAudioFocusChangeInter getOnAudioFocusChangeInter() {
        return mOnAudioFocusChangeInter;
    }

    public MAudioStateChangedByAudioFocusListener getAudioStateChangedByAudioFocusListener() {
        return mAudioStateChangedByAudioFocusListener;
    }

    public boolean isPlayerInitSuccess() {
        return isPlayerInitSuccess;
    }

    public void setPlayInitComplete(boolean flag){
        isPlayerInitSuccess = flag;
    }

    public int getAudioFocusStatus() {
        return mAudioFocusStatus;
    }


    public void addGeneralListener(IGeneralListener generalListener) {
        if (mGeneralListenerArrayList.contains(generalListener)) {
            return;
        }
        mGeneralListenerArrayList.add(generalListener);
    }

    public void removeGeneralListener(IGeneralListener generalListener) {
        if (PlayerPreconditions.checkNull(generalListener)) {
            return;
        }
        mGeneralListenerArrayList.remove(generalListener);
    }

    public void notifyGetPlayListError(PlayItem playItem, int errorCode, int errorExtra) {
        if (ListUtil.isEmpty(mGeneralListenerArrayList)) {
            return;
        }
        UIThreadUtil.runUIThread(() -> {
            for (IGeneralListener listener : mGeneralListenerArrayList) {
                if (listener != null) {
                    listener.getPlayListError(playItem, errorCode, errorExtra);
                }
            }
        });
    }

    public void notifyPlayError(int code) {
        if (ListUtil.isEmpty(mGeneralListenerArrayList)) {
            return;
        }
        UIThreadUtil.runUIThread(() -> {
            for (IGeneralListener listener : mGeneralListenerArrayList) {
                if (listener != null) {
                    listener.playUrlError(code);
                }
            }
        });


    }

    public boolean isPauseFromUser() {
        return isPauseFromUser;
    }

    public void setPauseFromUser(boolean pauseFromUser) {
        isPauseFromUser = pauseFromUser;
    }

    public void release() {
        if (!ListUtil.isEmpty(mPlayControlListenerArrayList)) {
            mPlayControlListenerArrayList.clear();
        }
        if (!ListUtil.isEmpty(mPlayerStateVideoListenerArrayList)) {
            mPlayerStateVideoListenerArrayList.clear();
        }
        if (!ListUtil.isEmpty(mPlayListControlListenerArrayList)) {
            mPlayListControlListenerArrayList.clear();
        }
        if (!ListUtil.isEmpty(mAudioFocusListenerArrayList)) {
            mAudioFocusListenerArrayList.clear();
        }
        if (!ListUtil.isEmpty(mPlayerInitCompleteListenerArrayList)) {
            mPlayerInitCompleteListenerArrayList.clear();
        }

        if (!ListUtil.isEmpty(mGeneralListenerArrayList)) {
            mGeneralListenerArrayList.clear();
        }
    }

}
