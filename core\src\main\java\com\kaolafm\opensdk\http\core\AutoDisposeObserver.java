package com.kaolafm.opensdk.http.core;

import android.app.Application;
import android.content.Context;
import android.os.Looper;
import android.os.RemoteException;
import android.util.Log;


import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.utils.FordDiagnosticsUtil;

import java.util.concurrent.atomic.AtomicReference;

import io.reactivex.disposables.Disposable;
import io.reactivex.internal.disposables.DisposableHelper;
import io.reactivex.internal.util.EndConsumerHelper;

/**
 * 用于自定义Observer、SingleObserver、DisposableSubscriber等
 * 请求结束后可以自动去掉的Observer
 * <AUTHOR>
 * @date 2018/6/11
 */
class AutoDisposeObserver<T> implements CompositeObserver<T> {

    private static final String TAG = "EventLog";
    private final AtomicReference<Disposable> mDisposableAtomicReference = new AtomicReference<>();

    private final HttpCallback<T> mCallback;

    AutoDisposeObserver(HttpCallback<T> callback) {
        mCallback = callback;
    }

    @Override
    public void dispose() {
        DisposableHelper.dispose(mDisposableAtomicReference);
    }

    @Override
    public boolean isDisposed() {
        return mDisposableAtomicReference.get() == DisposableHelper.DISPOSED;
    }

    @Override
    public void onSubscribe(Disposable d) {
        if (EndConsumerHelper.setOnce(this.mDisposableAtomicReference, d, getClass())) {
            onStart();
        }
    }

    protected void onStart() {
    }

    @Override
    public void onSuccess(T t) {
        success(t);
    }

    @Override
    public void onNext(T t) {
        success(t);
    }

    @Override
    public void onError(Throwable e) {
        error(e);
    }

    @Override
    public void onComplete() {
        dispose();
    }

    private void success(T s) {
        if (mCallback != null) {
            mCallback.onSuccess(s);
        }
        dispose();
    }

    private void error(Throwable e) {
        if (mCallback != null) {
            Log.i("TAG1","mCallback != null");
            if (e instanceof ApiException) {
                Log.i("TAG1","A");
                int code = ((ApiException) e).getCode();
                String message = ((ApiException) e).getMessage();
//                Log.i(TAG,"code is " + code);
//                Log.i(TAG,"message is " + message);
                if(!(code == 40000 || code == 40400 || code == 3)){
                    // 40000和40400 暂时先不上报
                    Log.i(TAG, "error: "+code);
                    Application app = ComponentKit.getInstance().getApplication();
                    Context context = app != null ? app.getApplicationContext() : null;
                    try {
                        report(context);
                    } catch (Exception ex) {
                        Log.i(TAG,"report crash " + ex);
                    }
                }
                mCallback.onError((ApiException) e);
            } else {
                Log.i("TAG1","B");
                String message = e.getMessage();
                Log.i("TAG1","message is ========" + message);
                mCallback.onError(new ApiException(e.getMessage()));
            }
        }else{
            Log.i(TAG,"mCallback == null");
        }
        dispose();

    }

    private void report(Context context){
        if (context != null) {
            FordDiagnosticsUtil.getInstance().reportFordDisconnect(context);
        }else{
            Log.i(TAG,"context == null");
        }
    }



}
